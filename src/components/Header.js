import React, { useState } from 'react';
import { useRouter } from 'next/router'; // Import useRouter
import { UserCircleIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';
import Cookies from 'js-cookie';

const Header = () => {
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const router = useRouter(); // Use useRouter()

    const handleLogout = () => {
        // Clear all cookies
        Cookies.remove('token');
        Cookies.remove('userId');
        Cookies.remove('username');
        Cookies.remove('email');
        Cookies.remove('role');
        
        // Redirect to login page
        router.push('/');
    };

    return (
        <header className="bg-[#004141] shadow p-4 flex items-center justify-between">
                    <div className="rounded-md pl-6 flex items-center justify-center h-full">
                        <Image
                            src={'/logo_footer.png'}
                            alt="Logo"
                            width={100}
                            height={100}
                            style={{ width: '100px', height: '100px' }}
                            className="object-contain cursor-pointer"
                            onClick={() => {
                                history.push('/dashboard');
                            }}
                        />
                    </div>
            <div className="relative">
                <UserCircleIcon
                    className="h-8 w-8 text-white cursor-pointer"
                    onClick={() => setDropdownOpen(!dropdownOpen)}
                />
                {dropdownOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white shadow-lg rounded-lg">
                        <ul className="py-1">
                            <li className="px-4 py-2 hover:bg-gray-100 cursor-pointer">Profile</li>
                            <li
                                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                onClick={handleLogout}
                            >
                                Logout
                            </li>
                        </ul>
                    </div>
                )}
            </div>
        </header>
    );
};

export default Header;