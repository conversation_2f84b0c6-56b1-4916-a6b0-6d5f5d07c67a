import React from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  HomeIcon
} from '@heroicons/react/24/outline';

const Sidebar = ({ activePage }) => {

  const menuItems = [
    {
      name: 'Dashboard Overview', // New item
      icon: HomeIcon,
      path: '/dashboard',
      badge: null // No badge needed
    },
    {
      name: 'Invoices',
      icon: DocumentTextIcon,
      path: '/invoiceApprovals',
      // badge: '12'
    },
    {
      name: 'Payments',
      icon: DocumentTextIcon,
      path: '/payments',
      badge: null,
    },
  ];

  return (
    <div className="h-10000 w-128 bg-[#004141] text-white flex flex-col">
      <nav>
        {menuItems.map((item) => (
          <Link
            key={item.name}
            href={item.path}
            className={`flex items-center justify-between p-3 mb-2 rounded-lg ${activePage === item.name.toLowerCase()
              ? 'bg-blue-600'
              : 'hover:bg-[#002D2D]'
              }`}
          >
            <div className="flex items-center">
              <item.icon className="h-6 w-6 mr-3" />
              <span>{item.name}</span>
            </div>
            {item.badge && (
              <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs">
                {item.badge}
              </span>
            )}
          </Link>
        ))}
      </nav>
    </div>
  );
};

export default Sidebar;