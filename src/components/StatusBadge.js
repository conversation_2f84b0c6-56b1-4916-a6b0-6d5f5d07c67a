import React from 'react';
import {
  CheckCircleIcon,
  XCircleIcon,
  QuestionMarkCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

/**
 * Maps status to appropriate icon and styling
 * @param {string} status - The status to display
 * @param {string} displayText - The text to display (optional, will use status if not provided)
 * @param {string} colorClass - The color class to use (optional)
 * @returns {JSX.Element} - A styled status badge with icon
 */
const renderStatus = (status, displayText, colorClass) => {
    // Default values
    let icon = <QuestionMarkCircleIcon className="w-5 h-5 mr-1.5 text-gray-500" aria-hidden="true" />;
    let statusText = displayText || status || 'Unknown';
    let bgColorClass = colorClass || 'text-gray-800 bg-gray-100';

    // Determine icon based on status
    // No clock icon for completed statuses
    if (status === 'DONE' ||
        status === 'VERIFIED_ANCHOR' ||
        status === 'APPROVED' ||
        status === 'ACCEPTED_LENDER' ||
        status === 'DISBURSED') {
        icon = <CheckCircleIcon className="w-5 h-5 mr-1.5 text-green-500" aria-hidden="true" />;
    }
    else if (status === 'REJECTED' ||
             status === 'REJECTED_ANCHOR' ||
             status === 'REJECTED_LENDER') {
        icon = <XCircleIcon className="w-5 h-5 mr-1.5 text-red-500" aria-hidden="true" />;
    }
    else if (status === 'MORE_INFO_NEEDED_ANCHOR' ||
             status === 'MORE_INFO_NEEDED_LENDER' ||
             status === 'INFO_NEEDED') {
        icon = <ExclamationTriangleIcon className="w-5 h-5 mr-1.5 text-orange-500" aria-hidden="true" />;
    }
    else if (status === 'UNDER_REVIEW' ||
             status === 'REVIEW' ||
             status === 'VERIFICATION_PENDING_LENDER' ||
             status === 'LOAN_IN_PROGRESS' ||
             status === 'REINITIATED') {
        icon = <ArrowPathIcon className="w-5 h-5 mr-1.5 text-yellow-500" aria-hidden="true" />;
    }
    else if (status === 'INITIATED' ||
             status === 'VERIFICATION_PENDING_ANCHOR') {
        icon = <ClockIcon className="w-5 h-5 mr-1.5 text-gray-500" aria-hidden="true" />;
    }

    return (
        <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-sm font-medium ${bgColorClass} whitespace-nowrap`}>
            {icon}
            {statusText}
        </span>
    );
};

/**
 * StatusBadge component for displaying status with appropriate styling and icon
 * @param {Object} props - Component props
 * @param {string} props.status - The status to display
 * @param {string} props.displayText - The text to display (optional)
 * @param {string} props.colorClass - The color class to use (optional)
 * @returns {JSX.Element} - A styled status badge with icon
 */
const StatusBadge = ({ status, displayText, colorClass }) => {
    return renderStatus(status, displayText, colorClass);
};

export default StatusBadge;
export { renderStatus };
