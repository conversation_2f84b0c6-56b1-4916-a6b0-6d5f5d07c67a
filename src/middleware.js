import { NextResponse } from 'next/server';

export function middleware(request) {
  const token = request.cookies.get('token');
  const { pathname } = request.nextUrl;

  // Define public paths that are accessible without a token
  const publicPaths = ['/', '/buyer-register'];

  // If the user is not logged in and trying to access a route that is NOT public
  if (!token && !publicPaths.includes(pathname)) {
    return NextResponse.redirect(new URL('/', request.url)); // Redirect to login page
  }

  // If the user is logged in and trying to access a public page (like login or register)
  // It's often good practice to redirect them to the dashboard.
  if (token && publicPaths.includes(pathname)) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  return NextResponse.next();
}

// Configure which routes are processed by this middleware
export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};