import React, { useState, useEffect, useRef } from 'react'; // Added useRef
import axios from 'axios';
import config from "../../config.json"
// Import date-fns functions for age calculation and filtering
import { parseISO, formatDistanceToNow, differenceInDays } from 'date-fns';
// Import necessary icons
import { CheckCircleIcon, XCircleIcon, InformationCircleIcon, DocumentMagnifyingGlassIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import { LinkIcon } from '@heroicons/react/24/solid'; // Using solid LinkIcon
import StatusBadge from '../../components/StatusBadge';

// --- Loader Component ---
const Loader = () => (
  <div className="flex items-center justify-center py-10">
    <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
    <p className="ml-3 text-gray-600">Loading...</p>
  </div>
);

// --- Helper function for calculating invoice age ---
// Uses 'uploadedAt' primarily, falls back to 'insertedOn' if available
const calculateInvoiceAge = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    const date = parseISO(dateString); // Parse the ISO string
    return formatDistanceToNow(date, { addSuffix: true }); // e.g., "about 2 hours ago"
  } catch (error) {
    console.error("Error parsing date for age calculation:", dateString, error);
    return 'Invalid Date';
  }
};


// --- Constants & Status Definitions ---
// Anchor-specific actions (statuses Anchor can SET)
const ANCHOR_STATUS_OPTIONS = {
  VERIFY: 'VERIFIED_ANCHOR',
  REJECT: 'REJECTED_ANCHOR',
  MORE_INFO: 'MORE_INFO_NEEDED_ANCHOR',
};

// All possible statuses for display, styling, and filtering in this view
// Includes statuses set by Anchor AND subsequent statuses
const STATUS_STYLES = {
  'VERIFICATION_PENDING_ANCHOR': { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-300' },
  'VERIFIED_ANCHOR': { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-300' },
  'REJECTED_ANCHOR': { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-300' },
  'MORE_INFO_NEEDED_ANCHOR': { bg: 'bg-orange-100', text: 'text-orange-800', border: 'border-orange-300' },
  'VERIFICATION_PENDING_LENDER': { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-300' },
  'ACCEPTED_LENDER': { bg: 'bg-sky-100', text: 'text-sky-800', border: 'border-sky-300' },
  'REJECTED_LENDER': { bg: 'bg-pink-100', text: 'text-pink-800', border: 'border-pink-300' },
  'MORE_INFO_NEEDED_LENDER': { bg: 'bg-amber-100', text: 'text-amber-800', border: 'border-amber-300' },
  'DISBURSED': { bg: 'bg-teal-100', text: 'text-teal-800', border: 'border-teal-300' },
  'LOAN_IN_PROGRESS': { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-300' },
  'DEFAULT': { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-300' }
};

// Display names for statuses relevant to this (Anchor) view
const STATUS_DISPLAY_NAMES = {
  'VERIFICATION_PENDING_ANCHOR': 'Pending Verification',
  'VERIFIED_ANCHOR': 'Verified by You',
  'REJECTED_ANCHOR': 'Rejected by You',
  'MORE_INFO_NEEDED_ANCHOR': 'More Info Requested',
  'UNKNOWN': 'Unknown Status' // Fallback
};

// --- Main Component (Anchor Invoice Verification) ---
const InvoiceApprovals = () => {
  // --- State ---
  const [isLoading, setIsLoading] = useState(false); // Loading for button actions
  const [isFetching, setIsFetching] = useState(true); // Loading for initial table fetch
  const [showPdfPreview, setShowPdfPreview] = useState(false); // Controls the details modal
  const [pdfUrl, setPdfUrl] = useState(''); // URL for main invoice preview in modal
  const [invoices, setInvoices] = useState([]); // Raw data fetched from API
  const [fetchError, setFetchError] = useState(null); // Error message during fetch
  const [selectedInvoice, setSelectedInvoice] = useState(null); // Full invoice object for the modal
  const [verificationComments, setVerificationComments] = useState(''); // Comments in the modal

  const [customDateRange, setCustomDateRange] = useState({ startDate: '', endDate: '' });
  const [invoiceNumberFilter, setInvoiceNumberFilter] = useState('');
  const [supplierNameFilter, setSupplierNameFilter] = useState('');
  const [amountRangeFilter, setAmountRangeFilter] = useState({ min: '', max: '' });
  const [useCustomDateRange, setUseCustomDateRange] = useState(false);

  // State for Upload Invoice functionality (if needed on this page)
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [mindeeData, setMindeeData] = useState(null);
  const [extractingData, setExtractingData] = useState(false);

  // --- NEW: Filter State ---
  const [statusFilter, setStatusFilter] = useState(''); // '' means 'All Statuses'
  const [ageFilter, setAgeFilter] = useState('');     // '' means 'Any Age'
  const [filteredInvoices, setFilteredInvoices] = useState([]); // Data displayed in the table after filtering
  // --- END Filter State ---

  // Refs (if needed, e.g., for file inputs)
  const fileInputRef = useRef(null); // For potential file input in upload modal

  // --- Effects ---
  useEffect(() => {
    fetchInvoices();
  }, []); // Fetch on initial mount

  // --- NEW: useEffect for Filtering ---
  // --- Enhanced useEffect for Filtering ---
  useEffect(() => {
    console.log("Filtering effect running with advanced filters");
    let tempInvoices = [...invoices]; // Start with the raw list

    // Apply Status Filter
    if (statusFilter) {
      tempInvoices = tempInvoices.filter(inv => inv?._doc?.status === statusFilter);
      console.log(`After status filter ('${statusFilter}'):`, tempInvoices.length);
    }

    // Apply Invoice Number Filter (case-insensitive partial match)
    if (invoiceNumberFilter.trim()) {
      tempInvoices = tempInvoices.filter(inv =>
        inv?._doc?.invoiceNumber?.toLowerCase().includes(invoiceNumberFilter.toLowerCase())
      );
      console.log(`After invoice number filter:`, tempInvoices.length);
    }

    // Apply Supplier Name Filter (case-insensitive partial match)
    if (supplierNameFilter.trim()) {
      tempInvoices = tempInvoices.filter(inv =>
        inv?._doc?.supplierName?.toLowerCase().includes(supplierNameFilter.toLowerCase())
      );
      console.log(`After supplier name filter:`, tempInvoices.length);
    }

    // Apply Amount Range Filter
    if (amountRangeFilter.min || amountRangeFilter.max) {
      tempInvoices = tempInvoices.filter(inv => {
        if (!inv?._doc?.totalAmount) return false;

        // Parse the amount - handle both string with currency and numbers
        const amount = typeof inv._doc.totalAmount === 'string'
          ? Number(inv._doc.totalAmount.replace(/[^0-9.-]+/g, ""))
          : Number(inv._doc.totalAmount);

        if (isNaN(amount)) return false;

        const min = amountRangeFilter.min ? Number(amountRangeFilter.min) : Number.MIN_SAFE_INTEGER;
        const max = amountRangeFilter.max ? Number(amountRangeFilter.max) : Number.MAX_SAFE_INTEGER;

        return amount >= min && amount <= max;
      });
      console.log(`After amount range filter:`, tempInvoices.length);
    }

    // Apply Date Filters - either custom range or predefined age
    if (useCustomDateRange && (customDateRange.startDate || customDateRange.endDate)) {
      // Convert string dates to Date objects for comparison
      const startDate = customDateRange.startDate ? new Date(customDateRange.startDate) : new Date(0); // Unix epoch start if not specified
      // Set endDate to end of the day
      const endDate = customDateRange.endDate ? new Date(customDateRange.endDate + 'T23:59:59') : new Date(); // Current date if not specified

      tempInvoices = tempInvoices.filter(inv => {
        const dateField = inv?._doc?.uploadedAt || inv?._doc?.insertedOn;
        if (!dateField) return false;

        try {
          const invoiceDate = new Date(dateField);
          return invoiceDate >= startDate && invoiceDate <= endDate;
        } catch (e) {
          console.error("Error parsing date for custom date filter:", dateField, e);
          return false;
        }
      });
      console.log(`After custom date range filter:`, tempInvoices.length);
    }
    // Apply predefined Age Filter (using uploadedAt or insertedOn)
    else if (ageFilter && !useCustomDateRange) {
      const now = new Date();
      tempInvoices = tempInvoices.filter(inv => {
        const dateField = inv?._doc?.uploadedAt || inv?._doc?.insertedOn;
        if (!dateField) return false;

        try {
          const invoiceDate = parseISO(dateField);
          const diffDays = differenceInDays(now, invoiceDate);

          switch (ageFilter) {
            case '7': return diffDays <= 7;
            case '30': return diffDays <= 30;
            case '90': return diffDays <= 90;
            case 'over90': return diffDays > 90;
            default: return true;
          }
        } catch (e) {
          console.error("Error parsing date for age filter:", dateField, e);
          return false;
        }
      });
      console.log(`After age filter ('${ageFilter}'):`, tempInvoices.length);
    }

    setFilteredInvoices(tempInvoices);
  }, [invoices, statusFilter, ageFilter, invoiceNumberFilter, supplierNameFilter, amountRangeFilter, customDateRange, useCustomDateRange]);
  // --- END Filtering useEffect ---

  // --- Helper Functions ---

  // Fetches invoices (Anchor perspective, including subsequent statuses)
  const fetchInvoices = async () => {
    console.log("[Anchor] Fetching invoices...");
    setIsFetching(true);
    setFetchError(null);
    try {
      // Fetch *all* invoices - filtering happens client-side for now
      const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchInvoices`);

      // Assuming API returns Mongoose objects with _doc
      if (response.data && Array.isArray(response.data)) {
        const fetchedInvoices = response.data;
        console.log(`[Anchor] Received ${fetchedInvoices.length} raw invoices from API.`);

        // Clean data: ensure _doc structure and filter out invalid entries
        const cleanedInvoices = fetchedInvoices
          .filter(inv => inv && (inv._doc || inv)) // Allow direct object or object with _doc
          .map(inv => ({ ...inv, _doc: inv._doc || inv })) // Ensure _doc structure
          .filter(inv => inv._doc && inv._doc._id && inv._doc.status); // Ensure essential fields exist

        // Sort: Pending/More Info first, then by date descending
        cleanedInvoices.sort((a, b) => {
          const statusOrder = {
            'VERIFICATION_PENDING_ANCHOR': 1,
            'MORE_INFO_NEEDED_ANCHOR': 2,
            // Other statuses get lower priority (higher number)
          };
          const orderA = statusOrder[a._doc.status] || 99;
          const orderB = statusOrder[b._doc.status] || 99;

          if (orderA !== orderB) return orderA - orderB; // Sort by actionability first

          // Then sort by most recent upload/insertion date
          const dateA = a._doc.uploadedAt || a._doc.insertedOn || 0;
          const dateB = b._doc.uploadedAt || b._doc.insertedOn || 0;
          try {
            return new Date(dateB) - new Date(dateA); // Descending (newest first)
          } catch (e) {
            console.error("Error comparing dates for sorting:", dateA, dateB, e);
            return 0;
          }
        });

        console.log(`[Anchor] Setting ${cleanedInvoices.length} cleaned and sorted invoices.`);
        setInvoices(cleanedInvoices); // Update the raw invoice list
        // The filtering useEffect will run after this state update
      } else {
        console.warn('[Anchor] Unexpected API response format or no data:', response);
        setInvoices([]);
      }
    } catch (error) {
      console.error('[Anchor] Error fetching invoices:', error.response?.data || error.message || error);
      const errorMsg = error.response?.data?.message || error.message || "An unknown error occurred while fetching invoices.";
      setFetchError(errorMsg);
      setInvoices([]);
    } finally {
      setIsFetching(false);
    }
  };

  // Gets display style based on status
  const getStatusStyle = (status) => {
    const style = STATUS_STYLES[status] || STATUS_STYLES['DEFAULT'];
    return `${style.bg} ${style.text}`;
  };

  // Gets display name for status
  const getStatusDisplay = (status) => {
    return STATUS_DISPLAY_NAMES[status] || status?.replace(/_/g, ' ') || STATUS_DISPLAY_NAMES['UNKNOWN'];
  };

  // Formats currency
  const formatAmount = (amount) => {
    if (amount === null || amount === undefined || amount === '') return 'N/A';
    if (typeof amount === 'string' && amount.startsWith('QAR')) { return amount; }
    // Try to convert safely, removing non-numeric except decimal point and negative sign
    const numAmount = Number(String(amount).replace(/[^0-9.-]+/g, ""));
    if (isNaN(numAmount)) {
      console.warn("formatAmount received non-numeric value:", amount);
      return 'Invalid';
    }
    // Use 'en-QA' for Qatar specific formatting if desired, else 'en-US'
    return `QAR ${numAmount.toLocaleString('en-QA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  // Extracts filename from path/URL
  const getFilenameFromPath = (path) => {
    if (!path) return 'document';
    try {
      // Handle full URLs first
      const url = new URL(path);
      const pathnameParts = url.pathname.split('/');
      return decodeURIComponent(pathnameParts[pathnameParts.length - 1] || 'document');
    } catch (e) {
      if (e) {
        console.log(e)
      }
      // If not a valid URL, treat as a simple path (handle both / and \)
      const pathParts = path.split(/[/\\]/); // Split by forward or backslash
      return decodeURIComponent(pathParts[pathParts.length - 1] || 'document');
    }
  };

  // --- Event Handlers ---

  // Opens the modal to view details and take action
  const handleViewInvoice = (invoice) => {
    // Ensure we have the necessary data structure (_doc and signedUrl)
    if (invoice && invoice._doc && invoice._doc.signedUrl) {
      console.log("[Anchor] Viewing invoice:", invoice._doc._id, "Status:", invoice._doc.status);
      setSelectedInvoice(invoice); // Store the full invoice object
      setPdfUrl(invoice._doc.signedUrl); // Set the URL for the iframe
      // Use existing comments or empty string
      setVerificationComments(invoice._doc.verificationComments || '');
      setShowPdfPreview(true); // Show the modal
      // Clear any lingering upload/Mindee state
      setMindeeData(null);
      setExtractingData(false);
      setUploadedFile(null);
    } else {
      console.error('[Anchor] Cannot view invoice - Missing _doc or signedUrl:', invoice);
      const errorMsg = "Could not load invoice document preview URL. Please check the invoice data.";
      setFetchError(errorMsg); // Show error to user
      alert(errorMsg);
    }
  };

  // Handles updating the invoice status via API call from the modal
  const handleStatusUpdate = async (newStatus) => {
    if (!selectedInvoice || !selectedInvoice._doc || !selectedInvoice._doc._id) {
      alert("Error: No invoice selected to update.");
      return;
    }
    // Require comments for specific actions
    if ((newStatus === ANCHOR_STATUS_OPTIONS.REJECT || newStatus === ANCHOR_STATUS_OPTIONS.MORE_INFO) && !verificationComments.trim()) {
      alert("Please provide comments when rejecting or requesting more information.");
      return;
    }

    console.log(`[Anchor] Updating invoice ${selectedInvoice._doc._id} to status ${newStatus} with comments: "${verificationComments}"`);
    setIsLoading(true); // Show loading state on buttons
    setFetchError(null); // Clear previous errors

    try {
      await axios.put(
        `${config.apiUrl}/ops/invoiceFinancing/updateInvoice/${selectedInvoice._doc._id}`,
        {
          status: newStatus,
          verificationComments: verificationComments // Send comments along with status
        }
        // Assuming token is handled by an interceptor or needed headers are added here if not
      );

      alert(`Invoice status updated to ${getStatusDisplay(newStatus)} successfully!`);
      setShowPdfPreview(false); // Close the modal
      setSelectedInvoice(null); // Clear selected invoice
      setVerificationComments(''); // Clear comments
      await fetchInvoices(); // Refresh the invoice list to show the change

    } catch (error) {
      console.error('[Anchor] Error updating invoice status:', error.response?.data || error.message);
      const errorMsg = error.response?.data?.message || error.message || 'Failed to update invoice status.';
      setFetchError(errorMsg); // Set error state to display if needed
      alert(`Update Failed: ${errorMsg}`);
    } finally {
      setIsLoading(false); // Stop loading state
    }
  };

  // --- Upload Invoice Handlers (Preserved, potentially unused if upload happens elsewhere) ---
  const handleFileUpload_UploadModal = async (e) => { // Renamed to avoid conflict if needed
    const file = e.target.files[0];
    if (file) {
      setUploadedFile(file);
      // Reset Mindee data until extraction is complete
      setMindeeData(null);
      setExtractingData(true);
      // Note: Showing preview immediately might not be ideal here if upload modal is simple
      // setShowPdfPreview(true);
      // setPdfUrl(URL.createObjectURL(file));

      try {
        const mindeeFormData = new FormData();
        mindeeFormData.append("document", file);
        // Use environment variables for API keys ideally
        const mindeeApiKey = "a22f7e4051a9178de0f37d3d7a49b17c"; // Replace with your key
        const mindeeResponse = await axios.post(
          "https://api.mindee.net/v1/products/mindee/invoices/v4/predict",
          mindeeFormData,
          { headers: { Authorization: `Token ${mindeeApiKey}`, "Content-Type": "multipart/form-data" } } // Correct Auth format
        );
        const data = mindeeResponse.data.document.inference.prediction;
        // Store extracted data directly, no need for _doc wrapper here yet
        setMindeeData({
          invoiceNumber: data.invoice_number?.value || '', // Default to empty string
          invoiceDate: data.date?.value || '',
          dueDate: data.due_date?.value || '',
          totalAmount: data.total_amount?.value || '',
          supplierName: data.supplier_name?.value || '',
          customerName: data.customer_name?.value || '',
          // Add other fields as needed by backend
        });
      } catch (mindeeError) {
        console.error('Error with Mindee API during upload:', mindeeError);
        alert("Failed to extract data automatically. Please ensure the PDF is a valid invoice.");
        setMindeeData(null); // Clear data on error
      } finally {
        setExtractingData(false);
      }
    }
  };

  const handleUploadSubmit_UploadModal = async () => { // Renamed to avoid conflict
    if (!uploadedFile) {
      alert("Please select an invoice PDF to upload.");
      return;
    }
    // Optionally check if mindeeData is needed before submission
    // if (!mindeeData && !allowManualEntry) {
    //     alert("Waiting for data extraction or provide details manually.");
    //     return;
    // }

    setIsLoading(true);
    setFetchError(null);
    try {
      const formData = new FormData();
      formData.append('pdfFile', uploadedFile);

      // Append data from mindeeData if available, otherwise expect manual input or backend handling
      if (mindeeData) {
        Object.keys(mindeeData).forEach(key => {
          if (mindeeData[key] !== null && mindeeData[key] !== undefined) {
            formData.append(key, mindeeData[key]);
          }
        });
      } else {
        // Handle case where Mindee failed - maybe prompt for manual entry or send minimal data
        console.warn("Submitting upload without Mindee data.");
        // formData.append('requiresManualReview', 'true'); // Example flag
      }
      // Set initial status expected by backend for anchor upload
      formData.append('status', "VERIFICATION_PENDING_ANCHOR");
      // Add userId if required by backend
      // formData.append('userId', userId);

      // *** Replace with your actual upload endpoint and token handling ***
      await axios.post(`${config.apiUrl}/ops/invoiceFinancing/uploadInvoice`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          // 'x-auth-token': token // Add token if needed
        }
      });

      alert("Invoice uploaded successfully! It will appear for verification shortly.");
      await fetchInvoices(); // Refresh the main list

      // Reset upload state
      setShowUploadModal(false);
      setUploadedFile(null);
      setMindeeData(null);
      // setPdfUrl(''); // pdfUrl might not be used in simple upload modal

    } catch (error) {
      console.error('Error uploading invoice:', error.response?.data || error.message);
      const errorMsg = error.response?.data?.message || error.message || 'Failed to upload invoice.';
      setFetchError(errorMsg); // Display error if needed
      alert(`Upload Failed: ${errorMsg}`);
    } finally {
      setIsLoading(false);
    }
  };

  // --- Render Functions ---

  // Renders Read-Only Details + Supporting Docs + Comments + Action Buttons inside the modal
  const renderInvoiceDetailsModalContent = (invoice) => {
    const invoiceData = invoice?._doc; // Use selected invoice's _doc

    if (!invoiceData) {
      return <div className="p-4 text-center text-red-600">Error: Could not load invoice details.</div>;
    }

    return (
      <>
        {/* Header */}
        <div className="mb-5 pb-3 border-b border-gray-200">
          <h3 className="text-base sm:text-lg font-semibold text-gray-800">Review Invoice</h3>
        </div>

        {/* Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3 text-sm mb-4">
          {renderDetailItem("Invoice Number", invoiceData.invoiceNumber)}
          {renderDetailItem("Buyer Name", invoiceData.supplierName)}
          {renderDetailItem("Invoice Date", invoiceData.invoiceDate)}
          {renderDetailItem("Trade/Legal Name", invoiceData.customerName)}
          {renderDetailItem("Due Date", invoiceData.dueDate)}
          <div className="col-span-1 md:col-span-2">
            <span className="text-sm font-medium text-gray-500 block mb-0.5">Current Status</span>
            <StatusBadge
              status={invoiceData.status}
              displayText={getStatusDisplay(invoiceData.status)}
              colorClass={getStatusStyle(invoiceData.status)}
            />
          </div>
        </div>

        {/* Amount */}
        <div className="my-4 p-3 bg-blue-50 rounded-lg border border-blue-100">
          <span className="text-sm font-medium text-blue-700 block mb-0.5">Total Amount Due</span>
          <div className="text-lg sm:text-xl font-bold text-blue-900">{formatAmount(invoiceData.totalAmount)}</div>
        </div>

        {/* Supporting Documents */}
        <div className="mt-4 pt-3 border-t border-gray-200">
          <h4 className="text-sm font-semibold text-gray-700 mb-2">Supporting Documents</h4>
          <div className="space-y-1.5 max-h-32 overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 text-sm border-t border-b py-2">
            {(invoiceData.additionalInvoiceDocuments && invoiceData.additionalInvoiceDocuments.length > 0) ? (
              invoiceData.additionalInvoiceDocuments.map((doc, index) => (
                doc && (
                  <div key={doc._id || index} className="flex items-center justify-between bg-gray-100 p-1.5 pl-2 rounded border border-gray-200">
                    <div className="flex items-center space-x-1.5 overflow-hidden mr-2"><LinkIcon className="h-3.5 w-3.5 text-gray-500 flex-shrink-0" /><span className="truncate text-gray-700" title={getFilenameFromPath(doc.filePath)}>{getFilenameFromPath(doc.filePath)}</span></div>
                    {doc.signedUrl ? (<a href={doc.signedUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline font-medium ml-2 flex-shrink-0 px-1.5 py-0.5 rounded hover:bg-blue-100" title={`View Document (Uploaded: ${doc.uploadedOn ? new Date(doc.uploadedOn).toLocaleDateString() : 'N/A'})`}>View</a>) : (<span className="text-gray-400 ml-2 flex-shrink-0 italic">No Link</span>)}
                  </div>
                )
              ))
            ) : (<p className="text-gray-500 italic text-sm py-2">No supporting documents found.</p>)}
          </div>
        </div>

        {/* Verification Comments */}
        <div className="mt-4 pt-3 border-t border-gray-200">
          <label htmlFor={`comments-${invoiceData._id}`} className="block text-sm font-medium text-gray-700 mb-1">Add/Edit Comments</label>
          <textarea
            id={`comments-${invoiceData._id}`}
            value={verificationComments}
            onChange={(e) => setVerificationComments(e.target.value)}
            // Disable if loading or if invoice is in a non-actionable state for Anchor
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition text-sm disabled:bg-gray-100 disabled:cursor-not-allowed"
            rows={3}
            placeholder="Add comments (required for rejection or requesting info)"
          />
        </div>

        {/* Action Buttons - Enable/disable based on current status */}

      </>
    );
  };

  // Helper to render individual read-only detail items in the modal
  const renderDetailItem = (label, value) => (
    <div className="bg-gray-50 rounded-md p-2 sm:p-3 border border-gray-100">
      <span className="text-sm font-medium text-gray-500 block mb-0.5">{label}</span>
      <span className="text-base sm:text-lg text-gray-800 font-medium break-words">{value || 'N/A'}</span>
    </div>
  );

  // --- Main JSX Return ---
  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 pb-4 border-b border-gray-200">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2 sm:mb-0">Buyer Invoice Verification</h1>
          <div className="flex space-x-3">
            {/* Refresh Button */}
            <button onClick={fetchInvoices} disabled={isFetching || isLoading} className="flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50" title="Refresh invoice list">
              <ArrowPathIcon className={`w-5 h-5 mr-2 ${isFetching ? 'animate-spin' : ''}`} />Refresh List
            </button>
            {/* Upload Button (Optional - if needed on this page) */}
            {/* <button className="flex items-center px-3 py-1.5 border border-transparent shadow-sm text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50" onClick={() => setShowUploadModal(true)} disabled={isLoading || isFetching}> <DocumentArrowUpIcon className="w-4 h-4 mr-2" /> Upload Invoice </button> */}
          </div>
        </div>

        {/* Active Filters Indicators */}
        {(statusFilter || ageFilter || invoiceNumberFilter || supplierNameFilter ||
          amountRangeFilter.min || amountRangeFilter.max ||
          customDateRange.startDate || customDateRange.endDate) && (
            <div className="mb-4 flex flex-wrap gap-2">
              <span className="text-sm text-gray-600">Active filters:</span>

              {statusFilter && (
                <div className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  Status: <StatusBadge
                    status={statusFilter}
                    displayText={getStatusDisplay(statusFilter)}
                    colorClass={getStatusStyle(statusFilter)}
                  />
                </div>
              )}

              {!useCustomDateRange && ageFilter && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  Age: {ageFilter === 'over90' ? 'Over 90 days' : `Last ${ageFilter} days`}
                </span>
              )}

              {useCustomDateRange && (customDateRange.startDate || customDateRange.endDate) && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  Date range: {customDateRange.startDate || 'Any'} to {customDateRange.endDate || 'Any'}
                </span>
              )}

              {invoiceNumberFilter && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  Invoice #: {invoiceNumberFilter}
                </span>
              )}

              {supplierNameFilter && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  Buyer: {supplierNameFilter}
                </span>
              )}

              {(amountRangeFilter.min || amountRangeFilter.max) && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  Amount: {amountRangeFilter.min ? `Min: ${formatAmount(amountRangeFilter.min)}` : ''}
                  {amountRangeFilter.min && amountRangeFilter.max ? ' - ' : ''}
                  {amountRangeFilter.max ? `Max: ${formatAmount(amountRangeFilter.max)}` : ''}
                </span>
              )}
            </div>
          )}

        {/* Error Display */}
        {fetchError && (<div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6 shadow-sm" role="alert"><strong className="font-bold">Error: </strong><span className="block sm:inline">{fetchError}</span></div>)}

        {/* --- Enhanced Filter UI --- */}
        <div className="mb-5 p-4 bg-white rounded-md shadow border border-gray-200">
          <div className="flex flex-col mb-4">
            <h3 className="text-base font-semibold text-gray-700 mb-2">Filter Invoices</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {/* Status Filter */}
              <div>
                <label htmlFor="statusFilterAnchor" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  id="statusFilterAnchor"
                  name="statusFilter"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                >
                  <option value="">All Statuses</option>
                  {Object.entries(STATUS_DISPLAY_NAMES)
                    .filter(([key]) => STATUS_STYLES[key])
                    .sort(([, a], [, b]) => a.localeCompare(b))
                    .map(([key, displayName]) => (
                      <option key={key} value={key}>{displayName}</option>
                    ))}
                </select>
              </div>

              {/* Invoice Number Filter */}
              <div>
                <label htmlFor="invoiceNumberFilter" className="block text-sm font-medium text-gray-700 mb-1">Invoice Number</label>
                <input
                  type="text"
                  id="invoiceNumberFilter"
                  value={invoiceNumberFilter}
                  onChange={(e) => setInvoiceNumberFilter(e.target.value)}
                  placeholder="Search by invoice #"
                  className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                />
              </div>

              {/* Supplier Name Filter */}
              <div>
                <label htmlFor="supplierNameFilter" className="block text-sm font-medium text-gray-700 mb-1">Buyer</label>
                <input
                  type="text"
                  id="supplierNameFilter"
                  value={supplierNameFilter}
                  onChange={(e) => setSupplierNameFilter(e.target.value)}
                  placeholder="Search by Buyer"
                  className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                />
              </div>

              {/* Date Filter Toggle */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date Filter Type</label>
                <div className="flex items-center space-x-4">
                  <label className="inline-flex items-center">
                    <input
                      type="radio"
                      checked={!useCustomDateRange}
                      onChange={() => setUseCustomDateRange(false)}
                      className="form-radio h-4 w-4 text-[#004141]"
                    />
                    <span className="ml-2 text-sm text-gray-700">Predefined</span>
                  </label>
                  <label className="inline-flex items-center">
                    <input
                      type="radio"
                      checked={useCustomDateRange}
                      onChange={() => setUseCustomDateRange(true)}
                      className="form-radio h-4 w-4 text-[#004141]"
                    />
                    <span className="ml-2 text-sm text-gray-700">Custom Range</span>
                  </label>
                </div>
              </div>
            </div>

            {/* Conditional Rendering Based on Date Filter Type */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-3">
              {/* Predefined Age Filter */}
              {!useCustomDateRange && (
                <div>
                  <label htmlFor="ageFilterAnchor" className="block text-sm font-medium text-gray-700 mb-1">Invoice Age</label>
                  <select
                    id="ageFilterAnchor"
                    name="ageFilter"
                    value={ageFilter}
                    onChange={(e) => setAgeFilter(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                  >
                    <option value="">Any Age</option>
                    <option value="7">Added Last 7 Days</option>
                    <option value="30">Added Last 30 Days</option>
                    <option value="90">Added Last 90 Days</option>
                    <option value="over90">Added Over 90 Days Ago</option>
                  </select>
                </div>
              )}

              {/* Custom Date Range */}
              {useCustomDateRange && (
                <>
                  <div>
                    <label htmlFor="startDateFilter" className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input
                      type="date"
                      id="startDateFilter"
                      value={customDateRange.startDate}
                      onChange={(e) => setCustomDateRange({ ...customDateRange, startDate: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                    />
                  </div>
                  <div>
                    <label htmlFor="endDateFilter" className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input
                      type="date"
                      id="endDateFilter"
                      value={customDateRange.endDate}
                      onChange={(e) => setCustomDateRange({ ...customDateRange, endDate: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                    />
                  </div>
                </>
              )}

              {/* Amount Range Filters */}
              <div>
                <label htmlFor="minAmountFilter" className="block text-sm font-medium text-gray-700 mb-1">Min Amount</label>
                <input
                  type="number"
                  id="minAmountFilter"
                  value={amountRangeFilter.min}
                  onChange={(e) => setAmountRangeFilter({ ...amountRangeFilter, min: e.target.value })}
                  placeholder="Minimum"
                  className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                />
              </div>
              <div>
                <label htmlFor="maxAmountFilter" className="block text-sm font-medium text-gray-700 mb-1">Max Amount</label>
                <input
                  type="number"
                  id="maxAmountFilter"
                  value={amountRangeFilter.max}
                  onChange={(e) => setAmountRangeFilter({ ...amountRangeFilter, max: e.target.value })}
                  placeholder="Maximum"
                  className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                />
              </div>
            </div>
          </div>

          {/* Filter Actions */}
          <div className="flex justify-end mt-4 border-t pt-4">
            <button
              onClick={() => {
                setStatusFilter('');
                setAgeFilter('');
                setInvoiceNumberFilter('');
                setSupplierNameFilter('');
                setAmountRangeFilter({ min: '', max: '' });
                setCustomDateRange({ startDate: '', endDate: '' });
                setUseCustomDateRange(false);
              }}
              className="px-4 py-2 mr-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Clear Filters
            </button>
            <div className="text-sm text-gray-600 flex items-center ml-3">
              {filteredInvoices.length} {filteredInvoices.length === 1 ? 'invoice' : 'invoices'} found
            </div>
          </div>
        </div>
        {/* --- END Enhanced Filter UI --- */}


        {/* Invoice Table */}
        <div className="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
          <div className="overflow-x-auto">
            <table className="w-full min-w-[800px]">
              <thead className="bg-gray-100 border-b border-gray-200">
                <tr>
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Invoice #</th>
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Inv Date</th>
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Due Date</th>
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Age</th> {/* Age Column */}
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Buyer</th>
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Trade/Legal Name</th>
                  <th className="p-3 text-right text-sm font-semibold text-gray-600 uppercase tracking-wider">Amount</th> {/* Align Right */}
                  <th className="p-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                  <th className="p-3 text-center text-sm font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {isFetching ? (
                  <tr><td colSpan="9" className="p-4 text-center"><Loader /></td></tr> // Updated colspan
                ) : filteredInvoices.length === 0 ? ( // Check filtered list
                  <tr><td colSpan="9" className="p-6 text-center text-sm text-gray-500">{(statusFilter || ageFilter) ? 'No invoices match filters.' : 'No invoices found.'}</td></tr> // Updated colspan and message
                ) : (
                  // Map over FILTERED invoices
                  filteredInvoices.map((invoice) => (
                    // Use invoice._doc if it exists, otherwise use invoice directly
                    (invoice && invoice._doc) && (
                      <tr key={invoice._doc._id} className="hover:bg-gray-50 transition-colors">
                        {/* Added align-top to all cells */}
                        <td className="p-3 align-top whitespace-nowrap">
                          <button onClick={() => handleViewInvoice(invoice)} className="text-blue-600 hover:text-blue-800 hover:underline font-medium text-sm" title={`View details for ${invoice._doc.invoiceNumber}`}> {invoice._doc.invoiceNumber || 'N/A'} </button>
                        </td>
                        <td className="p-3 align-top text-sm text-gray-600 whitespace-nowrap">{invoice._doc.invoiceDate || 'N/A'}</td>
                        <td className="p-3 align-top text-sm text-gray-600 whitespace-nowrap">{invoice._doc.dueDate || 'N/A'}</td>
                        {/* Age Cell */}
                        <td className="p-3 align-top text-sm text-gray-600 whitespace-nowrap"> {calculateInvoiceAge(invoice._doc.uploadedAt || invoice._doc.insertedOn)} </td>
                        <td className="p-3 align-top text-sm text-gray-700 max-w-[150px] truncate" title={invoice._doc.supplierName}>{invoice._doc.supplierName || 'N/A'}</td>
                        <td className="p-3 align-top text-sm text-gray-700 max-w-[150px] truncate" title={invoice._doc.customerName}>{invoice._doc.customerName || 'N/A'}</td>
                        <td className="p-3 align-top text-sm text-gray-900 font-semibold whitespace-nowrap text-right">{formatAmount(invoice._doc.totalAmount)}</td>
                        <td className="p-3 align-top text-sm whitespace-nowrap">
                          <StatusBadge
                            status={invoice._doc.status}
                            displayText={getStatusDisplay(invoice._doc.status)}
                            colorClass={getStatusStyle(invoice._doc.status)}
                          />
                        </td>
                        <td className="p-3 align-top text-center whitespace-nowrap">
                          <button onClick={() => handleViewInvoice(invoice)} className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" title="View Details & Actions">
                            <DocumentMagnifyingGlassIcon className="h-5 w-5 mr-1.5 text-gray-500" /> Details
                          </button>
                        </td>
                      </tr>
                    )
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* --- Upload Modal (If kept) --- */}
        {showUploadModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
            <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-lg">
              <h3 className="text-xl font-semibold mb-4">Upload Invoice</h3>
              <div className="space-y-4">
                {/* Simplified File Input */}
                <input type="file" ref={fileInputRef} className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" accept=".pdf" onChange={handleFileUpload_UploadModal} />
                {uploadedFile && !extractingData && (<p className="text-sm text-gray-600">Selected: {uploadedFile.name}</p>)}
                {extractingData && (<div className="flex items-center text-sm text-gray-600"><Loader /> Extracting data...</div>)}
                <div className="flex justify-end space-x-3 pt-3">
                  <button onClick={() => { setShowUploadModal(false); setUploadedFile(null); setMindeeData(null); setExtractingData(false); }} className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 text-sm font-medium">Cancel</button>
                  {/* Submit button might need adjustment based on workflow */}
                  <button onClick={handleUploadSubmit_UploadModal} className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm font-medium disabled:opacity-50" disabled={!uploadedFile || extractingData || isLoading}> {isLoading ? 'Submitting...' : (extractingData ? 'Processing...' : 'Upload & Submit')} </button>
                </div>
              </div>
            </div>
          </div>
        )}
        {/* --- End Upload Modal --- */}


        {/* --- Details/Action Modal --- */}
        {showPdfPreview && selectedInvoice && selectedInvoice._doc && (
          <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-[60] p-4 overflow-y-auto">
            <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl h-full max-h-[95vh] flex flex-col overflow-hidden">

              {/* Modal Header */}
              <div className="flex justify-between items-center px-5 py-4 border-b border-gray-200">
                <h3 className="text-lg sm:text-xl font-semibold text-gray-800">
                  Invoice Details: {selectedInvoice._doc.invoiceNumber || 'N/A'}
                </h3>
                <button
                  onClick={() => {
                    setShowPdfPreview(false);
                    setPdfUrl('');
                    setSelectedInvoice(null);
                    setVerificationComments('');
                  }}
                  className="text-gray-500 hover:text-gray-800 p-1 rounded-full hover:bg-gray-100 transition-colors"
                  aria-label="Close"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Modal Body */}
              <div className="flex-grow flex flex-col md:flex-row gap-4 overflow-hidden p-4">
                {/* Left: PDF Viewer */}
                <div className="w-full md:w-3/5 h-full border border-gray-200 rounded-lg overflow-hidden flex flex-col bg-gray-100">
                  <div className="p-2 bg-gray-200 border-b border-gray-300">
                    <p className="text-sm font-medium text-gray-700">Invoice Document</p>
                  </div>
                  {pdfUrl ? (
                    <iframe src={pdfUrl} className="w-full h-full border-0 min-h-[400px]" title="Invoice PDF Preview" />
                  ) : (
                    <div className="flex items-center justify-center flex-grow text-gray-500 p-10">
                      Invoice preview is not available.
                    </div>
                  )}
                </div>

                {/* Right: Details Pane */}
                <div className="w-full md:w-2/5 flex flex-col h-full">
                  <div className="flex-grow overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">
                    {renderInvoiceDetailsModalContent(selectedInvoice)}
                  </div>

                  {/* Sticky Action Buttons */}
                  <div className="sticky bottom-0 bg-white border-t border-gray-200 mt-2 pt-4 space-y-2 sm:space-y-0 sm:flex sm:space-x-3 p-3">
                    {/* Verify */}
                    <button
                      onClick={() => handleStatusUpdate(ANCHOR_STATUS_OPTIONS.VERIFY)}
                      disabled={isLoading || selectedInvoice._doc.status === ANCHOR_STATUS_OPTIONS.VERIFY || !['VERIFICATION_PENDING_ANCHOR', 'MORE_INFO_NEEDED_ANCHOR'].includes(selectedInvoice._doc.status)}
                      className="w-full sm:w-auto flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
                      title="Verify Invoice"
                    >
                      <CheckCircleIcon className="h-5 w-5 mr-2" /> Verify
                    </button>
                    {/* Reject */}
                    <button
                      onClick={() => handleStatusUpdate(ANCHOR_STATUS_OPTIONS.REJECT)}
                      disabled={isLoading || selectedInvoice._doc.status === ANCHOR_STATUS_OPTIONS.REJECT || !['VERIFICATION_PENDING_ANCHOR', 'MORE_INFO_NEEDED_ANCHOR'].includes(selectedInvoice._doc.status)}
                      className="w-full sm:w-auto flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
                      title="Reject Invoice"
                    >
                      <XCircleIcon className="h-5 w-5 mr-2 text-red-500" /> Reject
                    </button>
                    {/* More Info */}
                    <button
                      onClick={() => handleStatusUpdate(ANCHOR_STATUS_OPTIONS.MORE_INFO)}
                      disabled={isLoading || selectedInvoice._doc.status === ANCHOR_STATUS_OPTIONS.MORE_INFO || !['VERIFICATION_PENDING_ANCHOR'].includes(selectedInvoice._doc.status)}
                      className="w-full sm:w-auto flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
                      title="Request More Info"
                    >
                      <InformationCircleIcon className="h-5 w-5 mr-2 text-orange-500" /> Need More Info
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* --- End Details/Action Modal --- */}

      </div> {/* End Max Width Container */}
    </div> // End Main Page Div
  );
};

export default InvoiceApprovals;