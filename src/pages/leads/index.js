
import StatusBadge from '../../components/StatusBadge';

export default function LeadGeneration() {
  const leads = {
    creditLineApps: [
      { id: 1, company: "Tech Solutions Ltd", status: "Pre-qualified", score: 85 },
      { id: 2, company: "Global Trade Co", status: "Under Review", score: 72 }
    ],
    invoiceDiscounting: [
      { id: 3, company: "Manufacturing Pro", status: "Approved", score: 90 },
      { id: 4, company: "Supply Chain Inc", status: "Pending", score: 78 }
    ]
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Lead Generation</h1>

      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Credit Line Applications</h2>
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="p-4 text-left">Company</th>
                <th className="p-4 text-left">Status</th>
                <th className="p-4 text-left">Madad Score</th>
                <th className="p-4 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {leads.creditLineApps.map((lead) => (
                <tr key={lead.id}>
                  <td className="p-4">{lead.company}</td>
                  <td className="p-4">
                    <StatusBadge
                      status={lead.status === "Pre-qualified" ? "APPROVED" :
                              lead.status === "Under Review" ? "UNDER_REVIEW" :
                              lead.status === "Pending" ? "INITIATED" :
                              lead.status}
                      displayText={lead.status}
                    />
                  </td>
                  <td className="p-4">{lead.score}</td>
                  <td className="p-4">
                    <button className="bg-blue-500 text-white px-3 py-1 rounded">
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Invoice Discounting Requests</h2>
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="p-4 text-left">Company</th>
                <th className="p-4 text-left">Status</th>
                <th className="p-4 text-left">Madad Score</th>
                <th className="p-4 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {leads.invoiceDiscounting.map((lead) => (
                <tr key={lead.id}>
                  <td className="p-4">{lead.company}</td>
                  <td className="p-4">
                    <StatusBadge
                      status={lead.status === "Pre-qualified" ? "APPROVED" :
                              lead.status === "Under Review" ? "UNDER_REVIEW" :
                              lead.status === "Pending" ? "INITIATED" :
                              lead.status}
                      displayText={lead.status}
                    />
                  </td>
                  <td className="p-4">{lead.score}</td>
                  <td className="p-4">
                    <button className="bg-blue-500 text-white px-3 py-1 rounded">
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}