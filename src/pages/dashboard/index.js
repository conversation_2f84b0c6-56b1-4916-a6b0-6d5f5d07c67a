import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Bar } from 'react-chartjs-2';
import { Chart, registerables } from 'chart.js';
import { format } from 'date-fns'; // For date formatting
import StatusBadge from '../../components/StatusBadge';
import config from "../../config.json"

Chart.register(...registerables);

export default function DashboardPage() {
  const [invoices, setInvoices] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchInvoicesData = async () => {
      try {
        const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/fetchInvoices`);
        if (!response.ok) throw new Error('Failed to fetch invoices.');
        const data = await response.json();
        setInvoices(data.map(invoice => invoice._doc)); // Use only _doc
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    fetchInvoicesData();
  }, []);

  const totalInvoices = invoices.length;
  const pendingApprovals = invoices.filter((invoice) => ["VERIFICATION_PENDING_ANCHOR", "VERIFICATION_PENDING_LENDER"].includes(invoice.status)).length;
  const approvedInvoices = invoices.filter((invoice) => ["VERIFIED_ANCHOR", "ACCEPTED_LENDER"].includes(invoice.status)).length;
  const rejectedInvoices = invoices.filter((invoice) => ["REJECTED_ANCHOR", "REJECTED_LENDER"].includes(invoice.status)).length;
  const moreInfoNeeded = invoices.filter((invoice) => ["MORE_INFO_NEEDED_ANCHOR", "MORE_INFO_NEEDED_LENDER"].includes(invoice.status)).length;

  const chartData = {
    labels: ['Total', 'Pending', 'Approved', 'Rejected', 'More Info Needed'],
    datasets: [
      {
        label: 'Invoices Status',
        data: [totalInvoices, pendingApprovals, approvedInvoices, rejectedInvoices, moreInfoNeeded],
        backgroundColor: ['#4b5563', '#facc15', '#22c55e', '#ef4444', '#f97316'],
      },
    ],
  };

  const latestInvoices = invoices.slice(0, 5).map(invoice => ({
    invoiceNumber: invoice.invoiceNumber,
    customerName: invoice.customerName,
    dueDate: format(new Date(invoice.dueDate), 'dd MMM yyyy'),
    status: invoice.status,
    amount: parseFloat(invoice.totalAmount).toLocaleString('en-IN', { style: 'currency', currency: 'INR' })
  }));

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <h2 className="text-3xl font-bold mb-6 text-gray-800">Buyer Dashboard</h2>

      {loading && (
        <div className="flex justify-center items-center h-20">
          <div className="animate-spin h-10 w-10 border-4 border-gray-500 border-t-transparent rounded-full"></div>
        </div>
      )}

      {error && <div className="text-red-600 bg-red-100 p-4 rounded mb-4">Error: {error}</div>}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white shadow-md rounded-lg p-6 text-center">
          <p className="text-lg font-semibold text-gray-800">Total Invoices</p>
          <p className="text-3xl font-bold text-gray-700">{totalInvoices}</p>
        </div>
        <div className="bg-yellow-100 shadow-md rounded-lg p-6 text-center">
          <p className="text-lg font-semibold text-gray-800">Pending Approvals</p>
          <p className="text-3xl font-bold text-yellow-500">{pendingApprovals}</p>
        </div>
        <div className="bg-green-100 shadow-md rounded-lg p-6 text-center">
          <p className="text-lg font-semibold text-gray-800">Approved Invoices</p>
          <p className="text-3xl font-bold text-green-500">{approvedInvoices}</p>
        </div>
        <div className="bg-red-100 shadow-md rounded-lg p-6 text-center">
          <p className="text-lg font-semibold text-gray-800">Rejected Invoices</p>
          <p className="text-3xl font-bold text-red-500">{rejectedInvoices}</p>
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">Invoice Status Overview</h3>
        <Bar data={chartData} />
      </div>

      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">Latest Invoices</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-2 px-4 text-base font-semibold text-gray-600">Invoice #</th>
                <th className="text-left py-2 px-4 text-base font-semibold text-gray-600">Customer</th>
                <th className="text-left py-2 px-4 text-base font-semibold text-gray-600">Due Date</th>
                <th className="text-left py-2 px-4 text-base font-semibold text-gray-600">Status</th>
                <th className="text-left py-2 px-4 text-base font-semibold text-gray-600">Amount</th>
              </tr>
            </thead>
            <tbody>
              {latestInvoices.map((invoice, index) => (
                <tr key={index} className="border-b border-gray-100">
                  <td className="py-2 px-4 text-base text-gray-700">{invoice.invoiceNumber}</td>
                  <td className="py-2 px-4 text-base text-gray-700">{invoice.customerName}</td>
                  <td className="py-2 px-4 text-base text-gray-700">{invoice.dueDate}</td>
                  <td className="py-2 px-4 text-base text-gray-700">
                    <StatusBadge status={invoice.status} />
                  </td>
                  <td className="py-2 px-4 text-base text-gray-700">{invoice.amount}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="flex justify-center mt-8">
        <button
          onClick={() => router.push('/invoiceApprovals')}
          className="bg-blue-600 hover:bg-blue-700 px-6 py-3 text-lg text-white font-semibold rounded-lg shadow-md transition-all duration-300"
        >
          Go to Invoice Approvals
        </button>
      </div>
    </div>
  );
}