
import StatusBadge from '../../components/StatusBadge';

export default function Collections() {
  const collections = {
    onTime: [
      {
        id: 1,
        company: "Tech Solutions Ltd",
        amount: "QAR 10,00,000",
        dueDate: "2024-03-15",
        status: "On Time"
      }
    ],
    delayed: [
      {
        id: 2,
        company: "Global Trade Co",
        amount: "QAR 5,00,000",
        dueDate: "2024-02-01",
        daysDelayed: 5,
        status: "Delayed"
      }
    ],
    earlyWarning: [
      {
        id: 3,
        company: "Manufacturing Pro",
        amount: "QAR 15,00,000",
        dueDate: "2024-03-20",
        riskFactors: ["Payment History", "Market Conditions"],
        status: "Warning"
      }
    ]
  };

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Collections Dashboard</h1>

      {/* Summary Cards */}
      <div className="grid grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-600 text-sm">Total Collections Due</h3>
          <p className="text-2xl font-bold">QAR 30,00,000</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-600 text-sm">On-Time Payments</h3>
          <p className="text-2xl font-bold text-green-600">85%</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-600 text-sm">Delayed Payments</h3>
          <p className="text-2xl font-bold text-red-600">10%</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-600 text-sm">Early Warnings</h3>
          <p className="text-2xl font-bold text-yellow-600">5%</p>
        </div>
      </div>

      {/* On-Time Collections */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">On-Time Collections</h2>
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="p-4 text-left">Company</th>
              <th className="p-4 text-left">Amount</th>
              <th className="p-4 text-left">Due Date</th>
              <th className="p-4 text-left">Status</th>
              <th className="p-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {collections.onTime.map((item) => (
              <tr key={item.id} className="border-t">
                <td className="p-4">{item.company}</td>
                <td className="p-4">{item.amount}</td>
                <td className="p-4">{item.dueDate}</td>
                <td className="p-4">
                  <StatusBadge
                    status="DONE"
                    displayText="On Time"
                    colorClass="text-green-800 bg-green-100"
                  />
                </td>
                <td className="p-4">
                  <button className="bg-blue-500 text-white px-3 py-1 rounded">
                    View Details
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Delayed Payments */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Delayed Payments</h2>
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="p-4 text-left">Company</th>
              <th className="p-4 text-left">Amount</th>
              <th className="p-4 text-left">Due Date</th>
              <th className="p-4 text-left">Days Delayed</th>
              <th className="p-4 text-left">Status</th>
              <th className="p-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {collections.delayed.map((item) => (
              <tr key={item.id} className="border-t">
                <td className="p-4">{item.company}</td>
                <td className="p-4">{item.amount}</td>
                <td className="p-4">{item.dueDate}</td>
                <td className="p-4">{item.daysDelayed}</td>
                <td className="p-4">
                  <StatusBadge
                    status="REJECTED"
                    displayText="Delayed"
                    colorClass="text-red-800 bg-red-100"
                  />
                </td>
                <td className="p-4">
                  <button className="bg-blue-500 text-white px-3 py-1 rounded">
                    Take Action
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Early Warning Cases */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Early Warning Cases</h2>
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="p-4 text-left">Company</th>
              <th className="p-4 text-left">Amount</th>
              <th className="p-4 text-left">Due Date</th>
              <th className="p-4 text-left">Risk Factors</th>
              <th className="p-4 text-left">Status</th>
              <th className="p-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {collections.earlyWarning.map((item) => (
              <tr key={item.id} className="border-t">
                <td className="p-4">{item.company}</td>
                <td className="p-4">{item.amount}</td>
                <td className="p-4">{item.dueDate}</td>
                <td className="p-4">
                  <ul className="list-disc list-inside">
                    {item.riskFactors.map((factor, index) => (
                      <li key={index}>{factor}</li>
                    ))}
                  </ul>
                </td>
                <td className="p-4">
                  <StatusBadge
                    status="INFO_NEEDED"
                    displayText="Warning"
                    colorClass="text-orange-800 bg-orange-100"
                  />
                </td>
                <td className="p-4">
                  <button className="bg-blue-500 text-white px-3 py-1 rounded">
                    Review Risk
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}