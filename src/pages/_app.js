import '@/styles/globals.css';
import Sidebar from '@/components/Sidebar'; // Ensure this path is correct
import { useRouter } from 'next/router';
import Header from '@/components/Header';   // Standardized path, adjust if '../components/Header' is correct

export default function MyApp({ Component, pageProps }) {
  const router = useRouter();
  const currentPath = router.pathname;

  // Define an array of paths that should use the simple layout (no Header/Sidebar)
  const simpleLayoutPages = ['/', '/buyer-register'];

  // If the current path is one of the simple layout pages
  if (simpleLayoutPages.includes(currentPath)) {
    return <Component {...pageProps} />;
  }

  // For all other pages, render with Header and Sidebar
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <div className="flex flex-1">
        <Sidebar />
        <main className="flex-1 p-6">
          <Component {...pageProps} />
        </main>
      </div>
    </div>
  );
}