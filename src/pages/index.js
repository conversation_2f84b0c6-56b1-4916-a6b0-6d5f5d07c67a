import { useState } from 'react';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';
import config from "../config.json"

export default function Login() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const router = useRouter();

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const response = await fetch(
        `${config.apiUrl}/ops/invoiceFinancing/superadmin/login`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username, password }),
        }
      );

      const data = await response.json();

      if (response.ok) {
        // Successful login
        if (data.superadmin.role === 'buyerAdmin') {
          // Store authentication token in cookie
          Cookies.set('token', data.token, { expires: 1 }); // Expires in 1 day

          // Store user data in cookies
          Cookies.set('userId', data.superadmin._id, { expires: 1 });
          Cookies.set('username', data.superadmin.username, { expires: 1 });
          Cookies.set('email', data.superadmin.email, { expires: 1 });
          Cookies.set('role', data.superadmin.role, { expires: 1 });

          router.push('/dashboard');
        } else {
          setError('Invalid username or password');
        }
      } else {
        // Handle specific HTTP status codes
        if (response.status === 401) {
          setError('Unauthorized: Invalid credentials.');
        } else if (response.status === 404) {
          setError('Not Found: The requested resource was not found.');
        } else if (response.status === 500) {
          setError('Internal Server Error: Please try again later.');
        } else {
          setError(data.message || 'Login failed.');
        }
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('Failed to connect to the server.');
    }
  };

  return (
    <div style={{ backgroundImage: "url('https://i.ibb.co/NnxqP0cT/madad-background.jpg')" }} className="min-h-screen flex items-center justify-center
                   bg-cover                         // <-- Make image cover the area
                   bg-center                        // <-- Center the image
                   bg-no-repeat                     // <-- Prevent image tiling
                  ">
      {/* --- END OF MODIFIED SECTION --- */}      <div className="bg-white p-8 rounded-lg shadow-md w-96">
        <h1 className="text-2xl font-bold mb-6 text-center">Buyer Login</h1>
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Username
            </label>
            <input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="w-full p-2 border rounded-md"
              placeholder="Enter username"
              required
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Password
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-2 border rounded-md"
              placeholder="Enter password"
              required
            />
          </div>
          {error && <p className="text-red-500 text-sm mb-4">{error}</p>}
          <button
            type="submit"
            className="w-full bg-[#6fb468] text-white p-2 rounded-md hover:bg-blue-600"
          >
            Login
          </button>
        </form>
      </div>
    </div>
  );
}