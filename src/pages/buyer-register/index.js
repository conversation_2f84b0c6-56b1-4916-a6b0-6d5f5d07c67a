import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import config from "../../config.json"; // Adjust path if your config is elsewhere

export default function BuyerRegister() {
    const router = useRouter();

    // State for form fields
    const [displayBuyerName, setDisplayBuyerName] = useState(''); // For display from initial link
    const [email, setEmail] = useState(''); // Stores the PLAIN, DECODED buyer email for form submission
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');

    // State for page status
    const [isLoading, setIsLoading] = useState(true); // True while verifying link
    const [isEligible, setIsEligible] = useState(false); // True if link is valid and buyer can register
    const [error, setError] = useState(''); // Stores error messages
    const [successMessage, setSuccessMessage] = useState(''); // Stores success message after registration

    useEffect(() => {
        if (!router.isReady) {
            return; // Wait for router to be ready
        }

        const {
            name: queryName,
            buyerEmail: queryBuyerEmailFromLink,
            msmeEmail: queryMsmeEmailFromLink
        } = router.query;

        if (queryName) {
            try {
                setDisplayBuyerName(decodeURIComponent(queryName));
            } catch (e) {
                console.warn("Could not decode buyer name from query:", queryName, e);
                // setDisplayBuyerName(queryName); // Fallback to potentially encoded name
            }
        }

        if (!queryBuyerEmailFromLink || !queryMsmeEmailFromLink) {
            setError("Invalid registration link. Required information is missing.");
            setIsLoading(false);
            return;
        }

        let plainBuyerEmail = '';
        let plainMsmeEmail = '';

        try {
            // The link might have double-encoded emails (e.g., %2540).
            // router.query decodes once, so queryBuyerEmailFromLink might be "...%40...".
            // We need to decode it fully to a plain email string "...@...".
            let tempBuyerEmail = queryBuyerEmailFromLink;
            for (let i = 0; i < 2; i++) { // Attempt to decode up to 2 times to handle %2540 -> %40 -> @
                const decoded = decodeURIComponent(tempBuyerEmail);
                if (decoded === tempBuyerEmail) break; // Stop if no more decoding changes the string
                tempBuyerEmail = decoded;
            }
            plainBuyerEmail = tempBuyerEmail;

            let tempMsmeEmail = queryMsmeEmailFromLink;
            for (let i = 0; i < 2; i++) {
                const decoded = decodeURIComponent(tempMsmeEmail);
                if (decoded === tempMsmeEmail) break;
                tempMsmeEmail = decoded;
            }
            plainMsmeEmail = tempMsmeEmail;

        } catch (e) {
            console.error("Error decoding email parameters from link:", e);
            setError("Invalid format in registration link parameters. Please check the link.");
            setIsLoading(false);
            return;
        }

        setEmail(plainBuyerEmail); // Set state with the fully decoded email for form input and submission

        const verifyEligibility = async () => {
            setIsLoading(true); // Ensure loading is true for this async operation
            setError(''); // Clear previous errors
            try {
                // For the API call, ensure parameters are singly encoded.
                // encodeURIComponent will correctly handle plainBuyerEmail (e.g., "...@...").
                const encodedBuyerEmailForAPI = encodeURIComponent(plainBuyerEmail);
                const encodedMsmeEmailForAPI = encodeURIComponent(plainMsmeEmail);

                // CRITICAL: Ensure config.apiUrl points to your backend, e.g., "http://localhost:8080"
                // and does NOT have the "locahost" typo.
                const response = await fetch(
                    `${config.apiUrl}/ops/invoiceFinancing/verify-buyer-for-registration?buyerEmail=${encodedBuyerEmailForAPI}&msmeEmail=${encodedMsmeEmailForAPI}`
                );

                if (!response.ok) { // Catches HTTP errors like 404, 500 from fetch itself
                    const errorText = await response.text(); // Try to get error text
                    throw new Error(`Network response was not ok: ${response.status} ${response.statusText}. Details: ${errorText}`);
                }

                const data = await response.json();

                if (data.success) {
                    setIsEligible(true);
                    if (data.buyerName && !queryName) { // If API returns a name and link didn't have one
                        setDisplayBuyerName(data.buyerName);
                    }
                } else {
                    setError(data.message || "You are not eligible to register with this link. Please contact support.");
                    setIsEligible(false);
                }
            } catch (err) {
                console.error("Verification API error:", err);
                setError(err.message || "Could not verify registration eligibility. Please check the link or try again later.");
                setIsEligible(false);
            } finally {
                setIsLoading(false);
            }
        };

        verifyEligibility();

    }, [router.isReady, router.query]); // Dependencies

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError(''); // Clear previous submission errors
        setSuccessMessage('');

        if (password.length < 8) {
            setError("Password must be at least 8 characters long.");
            return;
        }
        if (password !== confirmPassword) {
            setError("Passwords do not match.");
            return;
        }

        setIsLoading(true);
        try {
            const registrationPayload = {
                username: email, // Plain email from state (e.g., "<EMAIL>")
                email: email,    // Plain email from state
                password: password,
                role: 'buyerAdmin',
                // If your backend /superadmin/create API can also store the buyer's name:
                // lenderName: displayBuyerName, // Assuming 'lenderName' field in SuperAdmin schema
            };

            const response = await fetch(
                `${config.apiUrl}/ops/invoiceFinancing/superadmin/create`, // API to create the BuyerAdmin user
                {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(registrationPayload),
                }
            );
            const data = await response.json();

            if (response.ok && data.success) {
                setSuccessMessage("Your buyer admin account has been created successfully! Redirecting to login...");
                setTimeout(() => {
                    router.push('/'); // Redirect to login page
                }, 3000);
            } else {
                setError(data.message || `Registration failed. Status: ${response.status}`);
            }
        } catch (err) {
            console.error('Registration submission error:', err);
            setError('Failed to connect to the server for registration. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    // 1. Show loading indicator
    if (isLoading) {
        return <div className="min-h-screen flex items-center justify-center"><p>Verifying registration link...</p></div>;
    }

    // 2. Show success message if registration was successful
    if (successMessage) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-100">
                <div className="bg-white p-8 rounded-lg shadow-md w-96 text-center">
                    <h1 className="text-2xl font-bold mb-4 text-green-600">Registration Successful!</h1>
                    <p className="text-gray-700 mb-6">{successMessage}</p>
                    <Link href="/" className="text-blue-600 hover:underline">
                        Proceed to Login
                    </Link>
                </div>
            </div>
        );
    }

    // 3. Show error message if verification failed or link is invalid
    if (error) { // This error could be from link validation or API call failure
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-100">
                <div className="bg-white p-8 rounded-lg shadow-md w-96 text-center">
                    <h1 className="text-2xl font-bold mb-4 text-red-600">Registration Problem</h1>
                    <p className="text-gray-700 mb-6">{error}</p>
                    <Link href="/" className="text-blue-600 hover:underline">
                        Go to Login Page
                    </Link>
                </div>
            </div>
        );
    }

    // 4. If eligible, show the registration form
    if (isEligible) {
        return (
            <div style={{ backgroundImage: "url('https://i.ibb.co/NnxqP0cT/madad-background.jpg')" }} className="min-h-screen flex items-center justify-center bg-cover bg-center bg-no-repeat">
                <div className="bg-white p-8 rounded-lg shadow-md w-96">
                    <h1 className="text-2xl font-bold mb-6 text-center">Buyer Admin Registration</h1>
                    <form onSubmit={handleSubmit}>
                        {displayBuyerName && (
                            <div className="mb-4">
                                <label htmlFor="buyerName" className="block text-gray-700 text-sm font-bold mb-2">
                                    Company Name
                                </label>
                                <input
                                    type="text"
                                    id="buyerName"
                                    value={displayBuyerName}
                                    className="w-full p-2 border rounded-md bg-gray-100"
                                    readOnly
                                />
                            </div>
                        )}
                        <div className="mb-4">
                            <label htmlFor="email" className="block text-gray-700 text-sm font-bold mb-2">
                                Email (Username)
                            </label>
                            <input
                                type="email"
                                id="email"
                                value={email}
                                className="w-full p-2 border rounded-md bg-gray-100"
                                readOnly
                                required
                            />
                        </div>
                        <div className="mb-4">
                            <label htmlFor="password" className="block text-gray-700 text-sm font-bold mb-2">
                                Create Password
                            </label>
                            <input
                                type="password"
                                id="password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                className="w-full p-2 border rounded-md"
                                placeholder="Enter new password"
                                required
                                minLength={8}
                            />
                        </div>
                        <div className="mb-6">
                            <label htmlFor="confirmPassword" className="block text-gray-700 text-sm font-bold mb-2">
                                Confirm Password
                            </label>
                            <input
                                type="password"
                                id="confirmPassword"
                                value={confirmPassword}
                                onChange={(e) => setConfirmPassword(e.target.value)}
                                className="w-full p-2 border rounded-md"
                                placeholder="Confirm new password"
                                required
                                minLength={8}
                            />
                        </div>
                        {/* Local form error for password mismatch etc. will be shown here by setError if handleSubmit calls it */}
                        {/* The 'error' state is also used by verifyEligibility, so a general error might appear here too if not handled by the block above. */}
                        {/* This specific spot is more for form-level validation errors from handleSubmit. */}
                        {/* If setError was called in handleSubmit, it will appear here */}
                        {/* {error && <p className="text-red-500 text-sm mb-4">{error}</p>} */}


                        <button
                            type="submit"
                            className="w-full bg-[#6fb468] text-white p-2 rounded-md hover:bg-blue-600 disabled:bg-gray-400"
                            disabled={isLoading} // Disable button while submitting form
                        >
                            {isLoading ? 'Processing...' : 'Create Account'}
                        </button>
                        <p className="text-sm text-center mt-4">
                            Already have an account?{' '}
                            <Link href="/" className="text-blue-600 hover:underline">
                                Login here
                            </Link>
                        </p>
                    </form>
                </div>
            </div>
        );
    }

    // Fallback: If not loading, no success, no error, and not eligible (e.g., initial state before API completes or if API call had an unhandled path)
    // This case should ideally be covered by the error display logic or a specific "not eligible" message.
    // If it reaches here, it's likely an unexpected state or very brief moment before router.push takes effect from an earlier block.
    if (!isLoading && !successMessage && !error && !isEligible) {
        // To prevent rendering anything if somehow conditions for error/success/eligibility are all false after loading
        // A redirect to login might be safest if this state is reached unexpectedly.
        console.warn("BuyerRegister: Reached unexpected rendering state. Redirecting to login.");
        if (router.isReady) router.push('/?reason=unexpected_state');
        return null;
    }


    return null; // Default return if no other condition is met (should ideally not be reached)
}