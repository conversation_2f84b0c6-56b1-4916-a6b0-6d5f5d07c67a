// src/pages/invoices/index.js
import { useState, useEffect } from 'react';
import axios from 'axios';
import StatusBadge from '../../components/StatusBadge';
import config from "../../config.json"

export default function InvoicesPage() {
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastAction, setLastAction] = useState(null);
  const [showNotification, setShowNotification] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const [viewMode, setViewMode] = useState('user'); // 'user' or 'all'
  const [isAdmin, setIsAdmin] = useState(false);

  // Status mapping between backend and frontend
  const statusMapping = {
    'VERIFICATION_PENDING': 'Uploaded',
    'VERIFIED': 'Verified by admin',
    'VERIFIED_BY_ANCHOR': 'Verified by anchor admin',
    'UPLOADED_AND_ELIGIBLE': 'Eligible for discounting',
    'APPLIED_FOR_DISCOUNTING': 'Applied',
    'REJECTED': 'Rejected'
  };

  // Reverse mapping for sending status updates to backend
  const reverseStatusMapping = {
    'Uploaded': 'VERIFICATION_PENDING',
    'Verified by admin': 'VERIFIED',
    'Verified by anchor admin': 'VERIFIED_BY_ANCHOR',
    'Eligible for discounting': 'UPLOADED_AND_ELIGIBLE',
    'Applied': 'APPLIED_FOR_DISCOUNTING',
    'Rejected': 'REJECTED',
    'Anchor': 'VERIFICATION_PENDING' // Default mapping for "Anchor" status
  };

  // Frontend status options
  const statuses = [
    "Anchor",
    "Uploaded",
    "Verified by admin",
    "Verified by anchor admin",
    "Eligible for discounting",
    "Applied",
    "Rejected"
  ];

  // Check if user is admin on component mount
  useEffect(() => {
    const userRole = localStorage.getItem('userRole') || '';
    setIsAdmin(userRole === 'admin' || userRole === 'superadmin');
  }, []);

  // Fetch invoices from the API
  const fetchInvoices = async () => {
    try {
      setLoading(true);

      let response;
      if (viewMode === 'all' && isAdmin) {
        // Fetch all invoices for admin users
        response = await axios.get(`${config.apiUrl}/fetchAllInvoices`);
      } else {
        // Get userId from localStorage or context if available
        const userId = localStorage.getItem('userId') || '';
        // Fetch user-specific invoices
        response = await axios.get(`${config.apiUrl}/fetchInvoices`, {
          params: { userId }
        });
      }

      // Transform API response to match UI needs
      const transformedInvoices = response.data.map(invoice => ({
        id: invoice._id,
        invoiceNumber: invoice.invoiceNumber,
        date: invoice.invoiceDate,
        dueDate: invoice.dueDate,
        customerName: invoice.customerName,
        amount: invoice.totalAmount,
        // Map backend status to frontend status
        status: statusMapping[invoice.status] || invoice.status,
        updatedAt: invoice.verifiedAt || invoice.appliedForDiscountingAt || invoice.uploadedAt,
        // Include user/company information (for admin view)
        companyName: invoice.companyName || 'Unknown Company',
        panNo: invoice.panNo || 'N/A',
        // Keep the original data for reference
        originalData: invoice
      }));

      setInvoices(transformedInvoices);
      setLoading(false);
    } catch (err) {
      console.error("Error fetching invoices:", err);
      setError("Failed to load invoices. Please try again.");
      setLoading(false);
    }
  };

  // Update invoice status
  const updateInvoiceStatus = async (invoiceId, newFrontendStatus) => {
    try {
      const backendStatus = reverseStatusMapping[newFrontendStatus];

      // Prepare update data
      const updateData = {
        status: backendStatus
      };

      // Add timestamping based on status
      if (backendStatus === 'VERIFIED' || backendStatus === 'VERIFIED_BY_ANCHOR') {
        updateData.verifiedAt = new Date().toISOString();
        updateData.verifiedBy = localStorage.getItem('username') || 'Unknown User';
      } else if (backendStatus === 'APPLIED_FOR_DISCOUNTING') {
        updateData.appliedForDiscountingAt = new Date().toISOString();
      }

      await axios.put(`${config.apiUrl}/updateInvoice/${invoiceId}`, updateData);

      // Refresh invoices after update
      fetchInvoices();

      // Show notification
      setLastAction({
        type: newFrontendStatus,
        invoiceId: invoiceId
      });
      setShowNotification(true);

    } catch (err) {
      console.error("Error updating invoice:", err);
      setError("Failed to update invoice status. Please try again.");
    }
  };

  // Load invoices on component mount and when viewMode changes
  useEffect(() => {
    fetchInvoices();
  }, [viewMode]);

  // Handle notification timeout
  useEffect(() => {
    if (showNotification) {
      const timer = setTimeout(() => {
        setShowNotification(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [showNotification]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setOpenDropdownId(null);
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const handleStatusUpdate = (invoiceId, newStatus) => {
    updateInvoiceStatus(invoiceId, newStatus);
    setOpenDropdownId(null);
  };

  const toggleDropdown = (e, invoiceId) => {
    e.stopPropagation();
    setOpenDropdownId(openDropdownId === invoiceId ? null : invoiceId);
  };

  const getStatusStyle = (status) => {
    const styles = {
      'Anchor': 'bg-purple-100 text-purple-800',
      'Uploaded': 'bg-yellow-100 text-yellow-800',
      'Verified by admin': 'bg-blue-100 text-blue-800',
      'Verified by anchor admin': 'bg-indigo-100 text-indigo-800',
      'Eligible for discounting': 'bg-green-100 text-green-800',
      'Applied': 'bg-teal-100 text-teal-800',
      'Rejected': 'bg-red-100 text-red-800'
    };
    return `px-2 py-1 rounded-full text-base ${styles[status] || 'bg-gray-100 text-gray-800'}`;
  };

  const getRowStyle = (status) => {
    if (status === 'Eligible for discounting') return 'bg-green-50';
    if (status === 'Applied') return 'bg-teal-50';
    if (status === 'Rejected') return 'bg-red-50';
    return 'hover:bg-gray-50';
  };

  const getStatusColorDot = (status) => {
    const colors = {
      'Anchor': 'bg-purple-500',
      'Uploaded': 'bg-yellow-500',
      'Verified by admin': 'bg-blue-500',
      'Verified by anchor admin': 'bg-indigo-500',
      'Eligible for discounting': 'bg-green-500',
      'Applied': 'bg-teal-500',
      'Rejected': 'bg-red-500'
    };
    return colors[status] || 'bg-gray-500';
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="p-6 flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
          <button
            onClick={fetchInvoices}
            className="mt-2 bg-red-100 text-red-800 px-3 py-1 rounded hover:bg-red-200"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 relative">
      {/* Notification Toast */}
      {showNotification && lastAction && (
        <div className="absolute top-4 right-4 p-4 rounded-lg shadow-lg text-white animate-fade-in-out transition-all duration-500 ease-in-out bg-blue-600 z-50">
          <p>Invoice status updated to: {lastAction.type}</p>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Invoices</h1>

        {/* Admin view toggle */}
        {isAdmin && (
          <div className="mb-4">
            <div className="bg-gray-100 rounded-lg p-1 inline-flex">
              <button
                onClick={() => setViewMode('user')}
                className={`px-4 py-2 rounded-md transition-colors ${viewMode === 'user'
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-700 hover:bg-gray-200'
                  }`}
              >
                My Invoices
              </button>
              <button
                onClick={() => setViewMode('all')}
                className={`px-4 py-2 rounded-md transition-colors ${viewMode === 'all'
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-700 hover:bg-gray-200'
                  }`}
              >
                All Invoices
              </button>
            </div>
          </div>
        )}

        <div className="flex flex-wrap gap-4">
          {statuses.map(status => (
            <div key={status} className="flex items-center space-x-2">
              <div className={`w-3 h-3 ${getStatusColorDot(status)} rounded-full`}></div>
              <span className="text-base">{status}</span>
            </div>
          ))}
        </div>
      </div>

      {invoices.length === 0 ? (
        <div className="bg-white p-8 rounded-lg shadow text-center">
          <p className="text-gray-500">No invoices found.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full bg-white shadow-md rounded-lg">
            <thead className="bg-gray-50">
              <tr>
                <th className="p-4 text-left text-base font-semibold">Invoice Number</th>
                <th className="p-4 text-left text-base font-semibold">Date</th>
                <th className="p-4 text-left text-base font-semibold">Due Date</th>
                {viewMode === 'all' && (
                  <>
                    <th className="p-4 text-left text-base font-semibold">Company</th>
                    <th className="p-4 text-left text-base font-semibold">PAN</th>
                  </>
                )}
                <th className="p-4 text-left text-base font-semibold">Customer</th>
                <th className="p-4 text-left text-base font-semibold">Amount</th>
                <th className="p-4 text-left text-base font-semibold">Status</th>
                <th className="p-4 text-left text-base font-semibold">Last Updated</th>
                <th className="p-4 text-left text-base font-semibold">Actions</th>
              </tr>
            </thead>
            <tbody>
              {invoices.map((invoice) => (
                <tr
                  key={invoice.id}
                  className={`border-t transition-all duration-300 ${getRowStyle(invoice.status)}`}
                >
                  <td className="p-4">
                    <button className="text-blue-500 hover:text-blue-700 hover:underline">
                      {invoice.invoiceNumber}
                    </button>
                  </td>
                  <td className="p-4">{formatDate(invoice.date)}</td>
                  <td className="p-4">{formatDate(invoice.dueDate)}</td>
                  {viewMode === 'all' && (
                    <>
                      <td className="p-4">{invoice.companyName}</td>
                      <td className="p-4">{invoice.panNo}</td>
                    </>
                  )}
                  <td className="p-4">{invoice.customerName}</td>
                  <td className="p-4">₹{Number(invoice.amount).toLocaleString()}</td>
                  <td className="p-4">
                    <StatusBadge
                      status={invoice.status}
                      displayText={invoice.status}
                      colorClass={getStatusStyle(invoice.status)}
                    />
                  </td>
                  <td className="p-4 text-base text-gray-600">
                    {formatDate(invoice.updatedAt)}
                  </td>
                  <td className="p-4">
                    <div className="flex space-x-2">
                      <div className="relative">
                        <button
                          onClick={(e) => toggleDropdown(e, invoice.id)}
                          className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 transition-colors flex items-center"
                        >
                          Update Status
                          <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </button>

                        {openDropdownId === invoice.id && (
                          <div className="absolute z-10 mt-1 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                            <div className="py-1" role="menu" aria-orientation="vertical">
                              {statuses.map((status) => (
                                <button
                                  key={status}
                                  onClick={() => handleStatusUpdate(invoice.id, status)}
                                  className={`w-full text-left px-4 py-2 text-base hover:bg-gray-100 ${invoice.status === status ? 'bg-blue-50 font-medium' : ''}`}
                                  role="menuitem"
                                >
                                  <div className="flex items-center">
                                    <div className={`w-2 h-2 ${getStatusColorDot(status)} rounded-full mr-2`}></div>
                                    {status}
                                    {invoice.status === status && (
                                      <svg className="w-4 h-4 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                      </svg>
                                    )}
                                  </div>
                                </button>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>

                      <button
                        onClick={() => window.location.href = `/invoices/${invoice.id}`}
                        className="bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600 transition-colors"
                      >
                        View
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}