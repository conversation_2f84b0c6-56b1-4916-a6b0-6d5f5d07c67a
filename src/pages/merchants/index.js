import React, { useState } from 'react';
import StatusBadge from '../../components/StatusBadge';

const MerchantWhitelisting = () => {
    const [filter, setFilter] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');

    // Hardcoded merchant data
    const merchants = {
        pending: [
            {
                id: 1,
                name: "Global Traders Inc",
                registrationDate: "2024-02-15",
                status: "Pending Review",
                category: "Wholesale",
                gstin: "27**********1ZV",
                panNumber: "**********",
                annualRevenue: "QAR 5,20,000",
                location: "Mumbai, Maharashtra",
                creditScore: "750"
            },
            {
                id: 2,
                name: "TechServe Solutions",
                registrationDate: "2024-02-18",
                status: "Documents Required",
                category: "IT Services",
                gstin: "29**********1ZV",
                panNumber: "**********",
                annualRevenue: "QAR 3,00,000",
                location: "Bengaluru, Karnataka",
                creditScore: "720"
            }
        ],
        processed: [
            {
                id: 3,
                name: "Manufacturing Pro Ltd",
                registrationDate: "2024-02-01",
                status: "Approved",
                category: "Manufacturing",
                gstin: "32**********1ZV",
                panNumber: "**********",
                annualRevenue: "QAR 8,00,000",
                location: "Pune, Maharashtra",
                creditScore: "780",
                processedDate: "2024-02-10"
            },
            {
                id: 4,
                name: "Retail Solutions Co",
                registrationDate: "2024-02-05",
                status: "Rejected",
                category: "Retail",
                gstin: "03**********1ZV",
                panNumber: "**********",
                annualRevenue: "QAR 2,00,000",
                location: "Delhi, NCR",
                creditScore: "640",
                processedDate: "2024-02-12"
            }
        ]
    };

    const getStatusStyle = (status) => {
        const styles = {
            'Pending Review': 'bg-yellow-100 text-yellow-800',
            'Documents Required': 'bg-orange-100 text-orange-800',
            'Approved': 'bg-green-100 text-green-800',
            'Rejected': 'bg-red-100 text-red-800'
        };
        return styles[status] || 'bg-gray-100 text-gray-800';
    };

    // Filter and search logic
    const filteredMerchants = [...merchants.pending, ...merchants.processed]
        .filter(merchant => {
            if (filter === 'pending') return merchant.status === 'Pending Review' || merchant.status === 'Documents Required';
            if (filter === 'approved') return merchant.status === 'Approved';
            if (filter === 'rejected') return merchant.status === 'Rejected';
            return true;
        })
        .filter(merchant =>
            merchant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            merchant.gstin.toLowerCase().includes(searchTerm.toLowerCase())
        );

    return (
        <div className="p-6 space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold">Merchant Whitelisting</h1>
                <div className="flex space-x-4">
                    <input
                        type="text"
                        placeholder="Search merchants..."
                        className="border rounded-md p-2"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <select
                        className="border rounded-md p-2"
                        value={filter}
                        onChange={(e) => setFilter(e.target.value)}
                    >
                        <option value="all">All Merchants</option>
                        <option value="pending">Pending Review</option>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                    </select>
                </div>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-4 gap-4">
                <div className="bg-white p-4 rounded-lg shadow-md">
                    <h3 className="text-gray-600 text-sm">Total Merchants</h3>
                    <p className="text-2xl font-bold">{merchants.pending.length + merchants.processed.length}</p>
                    <p className="text-sm text-gray-600">Registered merchants</p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-md">
                    <h3 className="text-gray-600 text-sm">Pending Review</h3>
                    <p className="text-2xl font-bold">{merchants.pending.length}</p>
                    <p className="text-sm text-gray-600">Awaiting verification</p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-md">
                    <h3 className="text-gray-600 text-sm">Approved</h3>
                    <p className="text-2xl font-bold">
                        {merchants.processed.filter(m => m.status === 'Approved').length}
                    </p>
                    <p className="text-sm text-gray-600">Whitelisted merchants</p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-md">
                    <h3 className="text-gray-600 text-sm">Rejected</h3>
                    <p className="text-2xl font-bold">
                        {merchants.processed.filter(m => m.status === 'Rejected').length}
                    </p>
                    <p className="text-sm text-gray-600">Failed verification</p>
                </div>
            </div>

            {/* Merchants Table */}
            <div className="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
                <table className="w-full">
                    <thead className="bg-gray-50">
                        <tr>
                            <th className="p-4 text-left">Merchant Name</th>
                            <th className="p-4 text-left">Category</th>
                            <th className="p-4 text-left">GSTIN</th>
                            <th className="p-4 text-left">PAN</th>
                            <th className="p-4 text-left">Revenue</th>
                            <th className="p-4 text-left">Credit Score</th>
                            <th className="p-4 text-left">Location</th>
                            <th className="p-4 text-left">Status</th>
                            <th className="p-4 text-left">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredMerchants.map((merchant) => (
                            <tr key={merchant.id} className="border-t hover:bg-gray-50">
                                <td className="p-4 font-medium">{merchant.name}</td>
                                <td className="p-4">{merchant.category}</td>
                                <td className="p-4">{merchant.gstin}</td>
                                <td className="p-4">{merchant.panNumber}</td>
                                <td className="p-4">{merchant.annualRevenue}</td>
                                <td className="p-4">{merchant.creditScore}</td>
                                <td className="p-4">{merchant.location}</td>
                                <td className="p-4">
                                    <StatusBadge
                                        status={merchant.status === "Pending Review" ? "UNDER_REVIEW" :
                                               merchant.status === "Documents Required" ? "INFO_NEEDED" :
                                               merchant.status === "Approved" ? "APPROVED" :
                                               merchant.status === "Rejected" ? "REJECTED" :
                                               merchant.status}
                                        displayText={merchant.status}
                                        colorClass={getStatusStyle(merchant.status)}
                                    />
                                </td>
                                <td className="p-4">
                                    <div className="flex space-x-2">
                                        <button className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600">
                                            View Details
                                        </button>
                                        {(merchant.status === 'Pending Review' || merchant.status === 'Documents Required') && (
                                            <>
                                                <button className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600">
                                                    Approve
                                                </button>
                                                <button className="bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600">
                                                    Reject
                                                </button>
                                            </>
                                        )}
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default MerchantWhitelisting;