
import StatusBadge from '../../components/StatusBadge';

export default function Approvals() {
  const pendingApprovals = [
    {
      id: 1,
      company: "Tech Solutions Ltd",
      type: "Credit Line",
      amount: "QAR 50,00,000",
      madadScore: 85,
      status: "Pending"
    },
    {
      id: 2,
      company: "Global Trade Co",
      type: "Invoice Discounting",
      amount: "QAR 25,00,000",
      madadScore: 78,
      status: "Under Review"
    }
  ];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Approvals</h1>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Pending Approvals</h2>
          <div className="space-x-2">
            <select className="border rounded-md p-2">
              <option>All Types</option>
              <option>Credit Line</option>
              <option>Invoice Discounting</option>
            </select>
            <select className="border rounded-md p-2">
              <option>All Status</option>
              <option>Pending</option>
              <option>Under Review</option>
            </select>
          </div>
        </div>

        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="p-4 text-left">Company</th>
              <th className="p-4 text-left">Type</th>
              <th className="p-4 text-left">Amount</th>
              <th className="p-4 text-left">Madad Score</th>
              <th className="p-4 text-left">Status</th>
              <th className="p-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {pendingApprovals.map((item) => (
              <tr key={item.id} className="border-t">
                <td className="p-4">{item.company}</td>
                <td className="p-4">{item.type}</td>
                <td className="p-4">{item.amount}</td>
                <td className="p-4">{item.madadScore}</td>
                <td className="p-4">
                  <StatusBadge
                    status={item.status === "Pending" ? "INITIATED" :
                            item.status === "Under Review" ? "UNDER_REVIEW" :
                            item.status}
                    displayText={item.status}
                  />
                </td>
                <td className="p-4">
                  <div className="flex space-x-2">
                    <button className="bg-green-500 text-white px-3 py-1 rounded">
                      Approve
                    </button>
                    <button className="bg-red-500 text-white px-3 py-1 rounded">
                      Reject
                    </button>
                    <button className="bg-blue-500 text-white px-3 py-1 rounded">
                      View
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}