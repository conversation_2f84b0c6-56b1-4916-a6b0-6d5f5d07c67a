class SharedCache {
  static tokenKey = "token";
  static user = "user";
  static partner = "partner";
  static hideFFLogo = "hideFFLogo";
  static app = null;

  static cache = null;

  /**
   * Initialize the cache with data
   * @param {Object} data - Initial data to populate the cache with
   */
  static init(data) {
      if (this.cache == null) {
          this.cache = data;
      }
      else {
          this.cache = {...this.cache, ...data};
      }

      // Store important data in local storage for persistence across page refreshes
      if (data.token) {
          localStorage.setItem(this.tokenKey, data.token);
      }
      if (data.user) {
          localStorage.setItem(this.user, JSON.stringify(data.user));
      }
      if (data.partner) {
          localStorage.setItem(this.partner, JSON.stringify(data.partner));
      }
  }

  /**
   * Get a value from the cache
   * @param {string} key - The key to retrieve
   * @returns {any} The stored value or null if not found
   */
  static get(key) {
      // Try to get from memory cache first
      if (this.cache && this.cache[key] !== undefined) {
          return this.cache[key];
      }
      
      // If not in memory cache, try localStorage
      if (key === this.tokenKey) {
          const token = localStorage.getItem(this.tokenKey);
          if (token) {
              if (!this.cache) this.cache = {};
              this.cache[key] = token;
              return token;
          }
      } 
      else if (key === this.user || key === this.partner) {
          const data = localStorage.getItem(key);
          if (data) {
              try {
                  const parsedData = JSON.parse(data);
                  if (!this.cache) this.cache = {};
                  this.cache[key] = parsedData;
                  return parsedData;
              } catch (e) {
                  console.error(`Error parsing ${key} from localStorage:`, e);
              }
          }
      }

      return null;
  }

  /**
   * Set a value in the cache
   * @param {string} key - The key to store the value under
   * @param {any} value - The value to store
   */
  static set(key, value) {
      // Initialize cache if not already initialized
      if (this.cache == null) {
          this.cache = {};
      }
      
      // Store in memory cache
      this.cache[key] = value;
      
      // Also store important values in localStorage for persistence
      if (key === this.tokenKey) {
          localStorage.setItem(this.tokenKey, value);
      } 
      else if (key === this.user || key === this.partner) {
          localStorage.setItem(key, JSON.stringify(value));
      }
  }

  /**
   * Remove a specific key from the cache
   * @param {string} key - The key to remove
   */
  static remove(key) {
      if (this.cache && this.cache[key] !== undefined) {
          delete this.cache[key];
      }
      
      localStorage.removeItem(key);
  }

  /**
   * Clear all values from the cache
   */
  static clear() {
      this.cache = null;
      localStorage.removeItem(this.tokenKey);
      localStorage.removeItem(this.user);
      localStorage.removeItem(this.partner);
      localStorage.removeItem(this.hideFFLogo);
  }

  /**
   * Check if the user is authenticated
   * @returns {boolean} True if the user is authenticated
   */
  static isAuthenticated() {
      return !!this.get(this.tokenKey);
  }
}

export default SharedCache;