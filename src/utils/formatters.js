import React from 'react';

/**
 * Format phone number with + prefix and make it clickable
 * @param {string|number} phoneNumber - The phone number to format
 * @returns {JSX.Element|string} - A clickable phone link or 'N/A' if no number
 */
export const formatPhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return 'N/A';
  
  // Clean the phone number (remove any existing + or spaces)
  const cleanNumber = phoneNumber.toString().replace(/[\s+\-()]/g, '');
  
  // Add + prefix if not already present
  const formattedNumber = cleanNumber.startsWith('+') ? cleanNumber : `+${cleanNumber}`;
  
  return (
    <a 
      href={`tel:${formattedNumber}`}
      className="text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200"
      title={`Call ${formattedNumber}`}
    >
      {formattedNumber}
    </a>
  );
};

/**
 * Format currency with proper locale formatting
 * @param {string|number} value - The currency value to format
 * @param {string} currency - The currency symbol (default: 'QAR')
 * @returns {string} - Formatted currency string
 */
export const formatCurrency = (value, currency = 'QAR') => {
  const numValue = (typeof value === 'string') ? parseFloat(value.replace(/,/g, '')) : value;
  if (numValue === null || numValue === undefined || isNaN(numValue)) return 'N/A';
  return `${currency} ${Number(numValue).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

/**
 * Format date to YYYY-MM-DD format
 * @param {string|Date} dateString - The date to format
 * @returns {string} - Formatted date string or 'N/A' if invalid
 */
export const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Invalid Date';
    return date.toLocaleDateString('en-CA'); // Format YYYY-MM-DD
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    return 'Invalid Date';
  }
};
