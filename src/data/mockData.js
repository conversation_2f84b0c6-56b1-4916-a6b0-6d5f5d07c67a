// src/data/mockData.js
export const mockInvoices = [
  {
    id: 1,
    invoiceNumber: "INV-2024-001",
    submissionDate: "2024-02-10",
    companyName: "ABC Manufacturing",
    amount: "QAR 2,50,000",
    status: "PENDING_APPROVAL",
    gstin: "27AAPFU0939F1ZV",
    category: "Manufacturing",
    cityState: "Mumbai, Maharashtra",
    updatedAt: "2024-02-11T10:30:00"
  },
  {
    id: 2,
    invoiceNumber: "INV-2024-002",
    submissionDate: "2024-02-11",
    companyName: "XYZ Industries",
    amount: "QAR 1,80,000",
    status: "PENDING_APPROVAL",
    gstin: "29AALFP8359F1ZV",
    category: "Industrial Goods",
    cityState: "Bengaluru, Karnataka",
    updatedAt: "2024-02-11T11:45:00"
  },
];

export const recentActivity = [
  {
    id: 1,
    timestamp: "2 hours ago",
    companyName: "ABC Manufacturing",
    action: "Invoice submitted for approval",
    amount: "QAR 2,50,000",
    status: "pending"
  },
  {
    id: 2,
    timestamp: "3 hours ago",
    companyName: "XYZ Industries",
    action: "Invoice validated",
    amount: "QAR 1,80,000",
    status: "processing"
  },
  {
    id: 3,
    timestamp: "5 hours ago",
    companyName: "PQR Enterprises",
    action: "Invoice approved",
    amount: "QAR 3,25,000",
    status: "approved"
  }
];

export const dashboardStats = {
  totalMSMEs: 45,
  activeMSMEs: 32,
  pendingApprovals: 15,
  totalInvoicesSubmitted: 156,
  totalInvoicesApproved: 128,
  averageProcessingTime: "1.5 days",
  approvalRate: "92%",
  todaySubmissions: 8
};